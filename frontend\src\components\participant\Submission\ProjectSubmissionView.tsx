import React, { useState, useEffect, useId, useContext, Children } from "react";
import ReactD<PERSON> from "react-dom/client";
import { Project, Team, projectFactory } from "@/modules";
import { widget, encrypt, cache, decrypt } from "@/utils";
import Loading from "@components/general/Loading";
import { LazyLoadImage } from "react-lazy-load-image-component";
import "react-lazy-load-image-component/src/effects/blur.css";
import ProjectRecommend from "../../projects/ProjectRecommend";
import { ProjectCardContext } from "@components/projects/Context";
import TeamAvatar from "@components/projects/Avatar";
import ReactMarkdown from "react-markdown";

type Props = {
  project?: Project;
  project_id?: string;
  onClose?: (e: any) => void; // for past winner card, winner card and admin dashboard, onClose is used to close the modal
  // isExplorePage?: boolean; // if not in project explore page, set to false to render ProjectList component in ProjectRecommendation section
};

// check if it is project page, if yes, then only render the recommendation section
const isProjectPage = window.location.pathname.includes("/projects");

const isShareProjectPage = window.location.pathname.includes("/projects/id");

//check if path is participant
const isParticipant = window.location.pathname.includes("participant");

//check if path is judge
const isJudge = window.location.pathname.includes("judge");

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
const isPostSubmission =
  import.meta.env.PUBLIC_PIXEL_IS_POST_SUBMISSION === "true";
const isPostEvent = import.meta.env.PUBLIC_PIXEL_IS_POST_EVENT === "true";

const ProjectSubmissionView: React.FC<Props> = ({
  project,
  project_id,
  onClose,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [projectData, setProjectData] = useState<Project | null>(project);
  const { closeProject } = useContext(ProjectCardContext);
  const imageId = useId();

  useEffect(() => {
    (async () => {
      try {
        let response: Project;

        // project_id is passed as props only in project/[id] (shared project link)
        // if project_id is provided, either fetch from cache of airtable
        if (project_id) {
          // check the cache if the project data is already fetched
          response = await cache.get(project_id);

          if (response && !isParticipant) {
            //there is a cache
            setProjectData(response);
          } else {
            // fetch data from airtable if there is no cache
            response = await projectFactory().getProject(project_id);
            //if response object is empty then redirect
            if (Object.keys(response).length === 0) {
              await widget.alertError("Sorry", "Project not found.");

              window.location.href = "/projects";
            }

            setProjectData(response);

            //cache the project data into client
            cache.set(project_id, response);
          }
        }

        if (projectData) {
          // change the video link to embed link
          if (projectData.video) {
            // Accept both YouTube and Google Drive links for the video
            // Prevent double-embedding if already an embed link
            const youtubeEmbedRegex = /youtube\.com\/embed\//i;
            const driveEmbedRegex = /drive\.google\.com\/file\/d\/.*\/preview/i;

            const youtubeMatch = !youtubeEmbedRegex.test(projectData.video)
              ? projectData.video.match(
                  /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com|youtu\.be)\/(?:watch\?v=)?([A-Za-z0-9_\-]+)/i
                )
              : null;
            const driveMatch = !driveEmbedRegex.test(projectData.video)
              ? projectData.video.match(
                  /(?:https?:\/\/)?(?:drive\.google\.com\/file\/d\/)([A-Za-z0-9_\-]+)(?:\/view)?/i
                )
              : null;

            if (youtubeMatch) {
              projectData.video =
                "https://youtube.com/embed/" + youtubeMatch[1].split("/").pop();
            } else if (driveMatch) {
              projectData.video =
                "https://drive.google.com/file/d/" + driveMatch[1] + "/preview";
            }
          }
        }

        // console.log("Project Data:", projectData);

        setIsLoading(false);
      } catch (error) {
        await widget.alertError("Sorry", "Something went wrong.", error);
      }
    })();
  }, []);

  useEffect(() => {
    (async () => {
      if (isShareProjectPage && projectData) {
        if (
          projectData.year == currentYear &&
          !isParticipant &&
          !(isPostSubmission || isPostEvent)
        ) {
          // if the project is from current year, redirect to /projects
          await widget.alertError(
            "Sorry",
            "This project is from the current year. Please wait until the post-event to view the project."
          );
          // remove the project data
          setProjectData(null);
          window.location.href = "/projects";
        }
      }
    })();
  }, [projectData]);

  async function downloadSlide() {
    const fileURL = projectData?.slide;
    const link = document.createElement("a");
    link.href = fileURL;
    link.target = "_blank";
    link.click();
  }

  function handleHighlightClick(e: any) {
    //the higlight will go like a modal fullscreen and the user can click on the background to close it
    const modal = document.createElement("div");
    modal.id = "modal";
    modal.classList.add(
      "fixed",
      "top-0",
      "top-0",
      "flex",
      "items-center",
      "justify-center",
      "w-screen",
      "h-screen",
      "bg-black",
      "bg-opacity-50",
      "z-50"
    );
    modal.addEventListener("click", (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });

    //in the modal will have the image src
    const img = document.createElement("img");
    img.src = e.target.src;
    img.classList.add("max-h-[80%]", "max-w-[80%]");
    modal.appendChild(img);
    document.body.appendChild(modal);
  }

  function copyProject(e: any) {
    let encryptedId: string = "";
    if (project_id) encryptedId = encrypt(project_id!);
    else encryptedId = encrypt(projectData!.id);
    navigator.clipboard.writeText(
      location.origin + "/projects/id/" + encryptedId
    );
    e.target.innerHTML = "COPIED!";
    e.target.classList.add("!bg-green-500");
    setTimeout(() => {
      e.target.classList.remove("!bg-green-500");
      e.target.innerHTML = "COPY LINK";
    }, 1500);
  }

  function shareProject() {
    //show widget alert to share the project by giving the link
    widget.alert({
      html: '<div id="shareproject"></div>',
      showCloseButton: true,
      showConfirmButton: false,
      didOpen: () => {
        ReactDOM.createRoot(
          document.querySelector("#shareproject") as HTMLElement
        ).render(
          <>
            <h1 className="font-bold text-start text-2xl ">Share Project</h1>
            <p className="opacity-70 mb-8 text-start">
              Click the button to copy the link to share to others.
            </p>
            <button
              className=" rounded-lg w-full py-5 text-center tracking-wide text-lg text-white bg-primary-6"
              onClick={copyProject}
            >
              COPY LINK
            </button>
          </>
        );
      },
    });
  }

  if (isLoading)
    return (
      <div
        id="loading-skeleton"
        className="project absolute bg-white rounded-t w-full h-full"
      >
        <div className="flex flex-col">
          <section
            className={`w-full h-fit modal-content px-4 md:px-5 xl:px-0 pt-10 lg:pb-10 mb-4`}
            id="child"
          >
            <div className="container-max-w mx-auto">
              <div className="flex flex-col justify-center items-center animate-pulse">
                {/* Project type */}
                <div className="mb-4">
                  <div className="flex gap-2 md:gap-5 justify-center">
                    <div className="w-28 h-8 bg-grey-2 rounded-full"></div>
                    <div className="w-28 h-8 bg-grey-2 rounded-full"></div>
                  </div>
                </div>
              </div>

              {/* Team Avatar */}
              <div
                className={`md:w-1/2 mx-auto flex justify-center mt-4 mb-16 gap-8 animate-pulse`}
              >
                <div className="w-16 h-16 bg-grey-2 rounded-full"></div>
              </div>

              {/* Project Thumbnail */}
              <div className="flex bg-white mb-10 aspect-video animate-pulse">
                <div className="w-full h-full bg-grey-2"></div>
              </div>

              {/* Presentation video */}
              <div className="aspect-video h-full mb-14 lg:mb-16 animate-pulse">
                <div className="w-full h-full bg-grey-2"></div>
              </div>

              {/* Project Description */}
              <div className="mb-14 lg:mb-16">
                <h2 className="font-semibold text-primary-linear mb-1">
                  Project Description
                </h2>
                <p className="font-semibold text-mobile-24 md:text-tablet-24 lg:text-desktop-34 mb-4">
                  What Is This Project About?
                </p>
                <div className="flex flex-col gap-2 animate-pulse">
                  <div className="w-full h-2 bg-grey-2 rounded-full"></div>
                  <div className="w-full h-2 bg-grey-2 rounded-full"></div>
                </div>
              </div>

              {/* Project Highlights */}
              <div className="w-full h-full mb-14 lg:mb-16">
                <h2 className="font-semibold text-primary-linear mb-1">
                  Project Highlights
                </h2>
                <p className="font-semibold text-mobile-24 md:text-tablet-24 lg:text-desktop-34 mb-4">
                  Take A Closer Look At The Project
                </p>

                <div className="grid md:grid-cols-2 gap-10 my-5 animate-pulse">
                  {[...Array(4)].map((_, i) => (
                    <div className="aspect-video h-full bg-grey-2" />
                  ))}
                </div>
              </div>

              {/* Tech Stack */}
              <div className="w-full mb-14 lg:mb-16">
                <div className="w-full grid-span-1">
                  <h2 className="font-semibold text-primary-linear mb-1">
                    Tech Stack
                  </h2>
                  <p className="font-semibold text-mobile-24 md:text-tablet-24 lg:text-desktop-34 mb-4">
                    Project Created Using
                  </p>
                  <div className="flex flex-row flex-wrap gap-3 animate-pulse">
                    {[...Array(3)].map((_, i) => (
                      <div className="w-24 h-10 bg-grey-2 rounded-full" />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    );

  return (
    <>
      <div className="project absolute bg-white rounded-t w-full h-full">
        <div className="z-[5] py-4 md:py-0 top-5 right-5 md:absolute flex flex-row md:flex-col items-center gap-3 border-b-[1px] border-b-[rgba(0,0,0,0.2)] md:border-none px-3 ">
          <div className="space-x-5 md:space-y-5 flex flex-row md:flex-col items-center ml-auto justify-center pl-3 md:pl-0 gap-3">
            {/* <div onClick={showEmptyState}>
              <i
                className="fa-solid fa-heart fa-xl opacity-50 hover:opacity-100 cursor-pointer"
                title="Like Project"
              ></i>
              </div> */}

            {/* show share project button only for site visitor */}
            {(!isParticipant ||
              isProjectPage ||
              isPostSubmission ||
              isPostEvent) &&
              !isJudge &&
              !isShareProjectPage && (
                <div className="!m-0 relative" onClick={shareProject}>
                  <i
                    className="fa-regular fa-share-from-square fa-xl text-grey-3 hover:text-grey-5 cursor-pointer"
                    title="Share Project"
                  ></i>
                </div>
              )}
          </div>

          {/* for /projects/[id] from share project link, dont show close modal button*/}
          {!project_id && (
            <button
              type="button"
              className="border border-grey-3 hover:bg-grey-5 text-grey-3 transition duration-200 hover:text-white rounded-md cursor-pointer text-sm p-1"
              title="Close Project"
              onClick={(e) => (onClose ? onClose(e) : closeProject(project.id))}
            >
              <i className="fa-solid fa-xmark p-1 pointer-events-none"></i>
            </button>
          )}
        </div>

        {projectData && (
          <>
            <section
              className={`z-[60] w-full h-fit modal-content px-4 md:px-5 xl:px-0 ${
                !project_id && "overflow-y-scroll h-full"
              } pt-10 pb-10 `}
              id="child"
            >
              <div className="container-max-w mx-auto">
                <div className="flex flex-col justify-center items-center">
                  {/* Project ID (shown only in Admin Dashboard) */}
                  {window.location.pathname.includes("admin") && (
                    <div className="text-center mb-4">
                      <span className="font-semibold">Project Airtable ID:</span>{" "}
                      <span className="text-primary-6">{projectData.id}</span>
                    </div>
                  )}

                  {/* Supervisor */}
                  <div className="text-center mb-8">
                    <span className="font-semibold">Supervised by:</span>{" "}
                    <a
                      href={projectData?.supervisor.profile_link}
                      target="_blank"
                      className="daisy-link hover:daisy-link-hover"
                    >
                      {projectData?.supervisor.name}
                    </a>
                  </div>

                  {/* Project type */}
                  <div className="text-center mb-4">
                    <div className="flex  gap-2 md:gap-5 justify-center ">
                      {/* Project Major */}
                      <div className="p-[1px] text-sm border bg-gradient-to-r from-[#74339D] to-[#8D4BAF] text-primary-5 rounded-full ">
                        <div
                          className="bg-white px-3 py-1 rounded-full hover:cursor-pointer hover:-translate-y-0.5 hover:shadow-xl transition duration-200"
                          onClick={() => {
                            if (!isPostSubmission || !isPostEvent) {
                              window.location.href = `/awards/${isPostEvent ? currentYear : parseInt(currentYear) - 1}/${projectData?.project_major}`;
                            } else {
                              window.location.href = `/projects/${projectData?.project_major}`;
                            }
                          }}
                        >
                          <p className="text-mobile-14 md:text-tablet-16">
                            {projectData?.project_major}
                          </p>
                        </div>
                      </div>

                      {/* Project SDG */}
                      <div className="p-[1px] text-sm border bg-gradient-to-r from-[#74339D] to-[#8D4BAF] text-primary-5 rounded-full">
                        <div className="bg-white px-3 py-1 rounded-full hover:cursor-pointer hover:-translate-y-0.5 hover:shadow-xl transition duration-200">
                          <p className="text-mobile-14 md:text-tablet-16">
                            SDG {projectData?.sdg}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Project Name */}
                  </div>
                </div>

                <h1 className="font-bold text-mobile-34 md:text-tablet-48 lg:text-desktop-60 text-center break-words mb-8">
                  {projectData?.project_name}
                </h1>
                <div
                  className={`md:w-1/2 mx-auto flex justify-center mt-4 mb-16 gap-8 text-center`}
                  style={{
                    gridTemplateColumns: `repeat(${projectData.team.members_id?.length}, minmax(200px}, 1fr))`,
                  }}
                >
                  <TeamAvatar
                    team={projectData.team}
                    size={"size-16 md:size-24"}
                    showName={true}
                    key={projectData.id}
                  />
                </div>

                {/* Project Thumbnail */}
                <div className="bg-white mb-10 aspect-video">
                  <LazyLoadImage
                    src={projectData.thumbnail}
                    alt={"thumbnail-" + imageId}
                    className="relative object-cover object-center rounded-lg"
                    effect="blur"
                  />
                </div>

                {/* Presentation video */}
                <div className="aspect-video h-full mb-14 lg:mb-16">
                  <iframe
                    src={projectData.video}
                    className="w-full h-full rounded object-cover"
                    allowFullScreen
                  ></iframe>

                  {/* close when submission deadline */}
                  {(isParticipant || isJudge) && projectData.slide && (
                    <button
                      //onclick download file
                      onClick={downloadSlide}
                      className="font-Roboto bg-primary-6 hover:bg-primary-7 transition duration-100 py-2 px-8 mt-6  mx-auto text-center text-white rounded mb-2 flex items-center w-fit gap-2 justify-center"
                    >
                      <i className="fa-solid fa-download"></i> Download
                      Presentation Slides
                    </button>
                  )}
                </div>

                {/* Project Description */}
                <div className="mb-14 lg:mb-16">
                  <h2 className="font-semibold text-primary-linear mb-1">
                    Project Description
                  </h2>
                  <p className="font-semibold text-mobile-24 md:text-tablet-24 lg:text-desktop-34 mb-4">
                    What Is This Project About?
                  </p>
                  <div className="prose prose-xs md:prose-sm lg:prose-base max-w-none text-base">
                    <ReactMarkdown>
                      {projectData?.project_desc || "No description provided"}
                    </ReactMarkdown>
                  </div>
                </div>

                {/* Project Highlights */}
                <div className="mb-14 lg:mb-16">
                  <h2 className="font-semibold text-primary-linear mb-1">
                    Project Highlights
                  </h2>
                  <p className="font-semibold text-mobile-24 md:text-tablet-24 lg:text-desktop-34 mb-4">
                    Take A Closer Look At The Project
                  </p>

                  <div className="grid md:grid-cols-2 gap-10 my-5">
                    {projectData.highlights?.length > 0 ? (
                      projectData.highlights?.map(
                        (highlight: any, i: number) => (
                          <div
                            key={"highlights-" + i}
                            className={`${
                              projectData.highlights.length > 1
                                ? "md:container"
                                : "col-span-2"
                            } relative aspect-video `}
                            onClick={handleHighlightClick}
                          >
                            <img
                              src={highlight}
                              alt={"highlight-" + i}
                              className="w-full h-full relative object-cover object-center rounded-lg cursor-pointer shadow hover:shadow-md transition"
                              width="100%"
                              height="100%"
                            />
                          </div>
                        )
                      )
                    ) : (
                      <p>No higlights provided</p>
                    )}
                  </div>
                </div>

                {/* Tech Stack */}
                <div className="w-full mb-14 lg:mb-16">
                  <div className="w-full grid-span-1 ">
                    <h2 className="font-semibold text-primary-linear mb-1">
                      Tech Stack
                    </h2>
                    <p className="font-semibold text-mobile-24 md:text-tablet-24 lg:text-desktop-34 mb-4">
                      Project Created Using
                    </p>
                    <div className="flex flex-row flex-wrap gap-3">
                      {projectData.tech_stack &&
                      projectData.tech_stack.length > 0 ? (
                        <>
                          {projectData.tech_stack.map(
                            (tag: string, i: number) => (
                              <span
                                key={"tech-stack-" + i}
                                className="bg-transparent border border-grey-3/50 hover:border-primary-7 duration-100 transition rounded-full font-medium px-5 py-2 hover:cursor-pointer"
                                onClick={() => {
                                  if (
                                    projectData.year == currentYear &&
                                    !isParticipant
                                  ) {
                                    window.location.href = `/projects/tech/${tag}`;
                                  }
                                }}
                              >
                                {tag}
                              </span>
                            )
                          )}
                        </>
                      ) : (
                        "No tech stack provided"
                      )}
                    </div>
                  </div>
                </div>

                {/* IMPROVEMENT NEEDED: when click on a project card under project recommendation, it is causing lag and malfunction of modal open after all modals have been closed */}
                {/* Showcase */}
                {/* {isProjectPage &&
                  !isParticipant &&
                  projectData.year == import.meta.env.PUBLIC_PIXEL_YEAR && (
                    <>
                      <h2 className="font-semibold text-primary-linear mb-1">
                        More Showcase
                      </h2>
                      <p className="font-semibold text-mobile-24 md:text-tablet-24 lg:text-desktop-34 mb-4">
                        Explore Similar Projects
                      </p>
                      <ProjectRecommend project={projectData} />
                    </>
                  )} */}
              </div>
            </section>
          </>
        )}
      </div>
    </>
  );
};

export default ProjectSubmissionView;
