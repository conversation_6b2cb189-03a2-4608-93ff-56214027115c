import React, { useState, useRef, useEffect } from "react";
import { widget, auth, actionSessionStorage } from "@/utils";
import { authFactory } from "@modules/auth/bloc";
import ReCAPTCHA from "react-google-recaptcha";
import { LazyLoadImage } from "react-lazy-load-image-component";
import "react-lazy-load-image-component/src/effects/blur.css";
import { onAuthStateChanged } from "firebase/auth";
import { checkRecaptcha } from "@modules/auth/index";
import judges from "@data/judges.json";
import admins from "@data/admins.json";

const isRegistration = import.meta.env.PUBLIC_PIXEL_IS_REGISTRATION === "true";
const isOfficialLaunch =
  import.meta.env.PUBLIC_PIXEL_IS_OFFICIAL_LAUNCH === "true";

const needRecptcha = import.meta.env.PUBLIC_PIXEL_NEED_RECAPTCHA === "true";

const version = import.meta.env.PUBLIC_PIXEL_VERSION || "1.0.0";

const SignIn = () => {
  const [signInCredential, setSignInCredential] = useState({
    email: "",
    password: "",
  });
  const [recaptchaVerified, setRecaptchaVerified] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const recaptchaRef = useRef(null);

  // user can access sign in page when official launch is true
  if (!isOfficialLaunch) {
    widget
      .alertError(
        "Registration is closed for now",
        "Please wait for the official launch.",
        undefined,
        3000
      )
      .then(() => {
        window.location.href = "/";
      });
    return;
  }

  useEffect(() => {
    // save version to session storage if empty
    const versionParts = version.split(".");
    if (!actionSessionStorage().get("version")) {
      actionSessionStorage().create("version", {
        major: versionParts[0],
        minor: versionParts[1],
        patch: versionParts[2],
      });
    } else {
      // if version major or minor is not the same, update the version in session storage
      if (
        actionSessionStorage().get("version").major !== versionParts[0] ||
        actionSessionStorage().get("version").minor !== versionParts[1]
      ) {
        actionSessionStorage().clear();
        actionSessionStorage().update("version", {
          major: versionParts[0],
          minor: versionParts[1],
          patch: versionParts[2],
        });
      }
    }

    let unsub;
    // validation when entering register page
    (function () {
      // user here is the return object from the firebase authentication method, onAuthStateChanged
      // if the user is logged in, then the user object will be returned
      // if the user is not logged in, then the user object will be null
      // used as flag
      unsub = onAuthStateChanged(auth, (user) => {
        // Make sure the user uid is initialized or not null all the time
        var judgesList = judges.map((judge) => judge.toLowerCase());

        const isJudge = judgesList.includes(user?.email);

        const isAdmin = admins.includes(user?.email);

        if (isAdmin) {
          return;
        }

        if (unsub) {
          unsub();
        }

        if (user) {
          // uncomment this to skip email verification
          // let judges bypass email verification
          // if participant is not verified, redirect to sign in page
          if (!isJudge && !user.emailVerified) {
            widget
              .alertError(
                "Email not verified",
                "Please verify your email before proceeding."
              )
              .then(() => {
                window.location.href = "/sign-in";
                setTimeout(() => {
                  actionSessionStorage().destroy("userData");
                  auth.signOut();
                }),
                  1000;
              });
          } else {
            const user = actionSessionStorage().get(
              isJudge ? "judgeData" : "userData"
            );

            // if there is a user data in the session storage, meaning that the user has logged in before, redirect to the dashboard
            if (user) {
              widget
                .alert({
                  title: "You have logged in",
                  text: `Sign out to switch account. Redirecting to ${
                    isJudge ? "judge" : "participant"
                  } dashboard ...`,
                  icon: "info",
                  timer: 3000,
                  timerProgressBar: true,
                })
                .then(() => {
                  window.location.href = `/${
                    isJudge ? "judge" : "participant"
                  }`;
                });
            }
          }
        }
      });
    })();
  }, []);

  // Spread operator is used to copy the existing data within the signInCredential
  // This reduces the need to manually copy the existing data and update the new data
  const handleInputChange = (e) => {
    // {} defines the object literal: the LHS is the structure/variable/key; the RHS are the values.
    setSignInCredential({
      ...signInCredential,
      [e.target.name]: e.target.value,
    });
  };

  const onRecaptchaChange = () => {
    setRecaptchaVerified(true);
  };

  const handleForgotPassword = async () => {
    const confirm = await widget.alert({
      input: "email",
      text: "Enter your email address to reset your password",
      showCancelButton: true,
      allowOutsideClick: false,
    });
    // check field not empty
    if (confirm.value.trim().length < 1) return;
    // check if user click cancel
    if (!confirm.isConfirmed) return;
    try {
      await authFactory().sendResetPasswordEmail(confirm.value);
    } catch (error) {
      await widget.alertError("Error", error.message);
    }
  };

  const toggleShowPassword = (e: any) => {
    setShowPassword(!showPassword);
    const password = document.getElementById("password") as HTMLInputElement;
    password.type = showPassword ? "password" : "text";
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (needRecptcha) {
      if (!recaptchaVerified) {
        await widget.alertError("Alert", "Please proceed with ReCAPTCHA.");
        return;
      }

      const token = recaptchaRef.current.getValue();
      recaptchaRef.current.reset();
      if (!(await checkRecaptcha(token))) {
        return;
      }
    }

    try {
      widget.loading();
      await authFactory().signIn(signInCredential);
    } catch (error) {
      let errorMessage = error.message;
      await widget.error(errorMessage);
      // showButtonLoading("loginBtn", false, "Sign In");
    }
  };

  return (
    <div className="w-full flex max-lg:h-[100dvh] h-full">
      <div className="max-lg:hidden w-[50%] border-r-2 flex border-grey-2 *:text-[100px] *:leading-none *:font-bold overflow-hidden">
        <div className="h-full self-end relative -bottom-28 hover:-bottom-16 transition-all">
          <LazyLoadImage
            src="/assets/images/logo/pixie-half.webp"
            alt="PIXEL Pixie"
            className=""
            effect="blur"
          />
        </div>
      </div>
      <div className="h-full w-full flex flex-col justify-center lg:w-[50%] px-5 sm:px-10 sm:py-16 md:px-16 md:py-12 ">
        <a
          href="/"
          className="text-mobile-14 mb-10 sm:mb-5 cursor-pointer hover:underline  "
        >
          <i className="fa-solid fa-chevron-left text-sm mr-1"></i>
          <span className="text-mobile-14">Return to Home</span>
        </a>

        <div className="mb-5">
          <div className="w-[3rem] h-[3rem] my-3 mb-5 max-sm:mx-auto">
            <LazyLoadImage
              src="assets/images/logo/PIXEL-icon.webp"
              alt="pixel"
              width="100%"
              height="100%"
              effect="blur"
            />
          </div>
          <h1 className="text-2xl font-bold max-sm:text-center mb-1">
            Account Login
          </h1>
          <p className="text-mobile-14 max-sm:text-center">
            To get started, login with your account.
          </p>
        </div>
        <form className="my-5" onSubmit={handleSubmit}>
          {/* email input */}
          <div className="flex items-center input-container mb-5">
            <label htmlFor="email" className="sr-only">
              Email Address
            </label>
            <input
              type="text"
              name="email"
              className="w-full border-none px-0"
              required
              autoComplete="email"
              value={signInCredential.email} // access the email property from the signInCredential structure
              onChange={handleInputChange} // This helps to update the state of the email input field
            />
            <span>Email Address</span>
            <i className="fa-solid fa-at absolute right-2 text-grey-3"></i>
          </div>

          {/* password input */}
          <div>
            <div className="flex items-center input-container mb-5">
              <label className="sr-only" htmlFor="password">
                Password
              </label>
              <input
                type="password"
                id="password"
                name="password"
                className="w-full border-none px-0"
                required
                autoComplete="current-password"
                value={signInCredential.password}
                onChange={handleInputChange} // This helps to update the state of the password input field
              />
              <span>Password</span>
              <i
                className={` ${
                  showPassword ? "fa-regular fa-eye" : "fa-regular fa-eye-slash"
                }  absolute right-2 z-50 text-grey-4 hover:cursor-pointer`}
                id="password-toggle"
                onClick={(e) => toggleShowPassword(e)}
              ></i>
            </div>
            <p
              id="forgot-password"
              className="text-grey-4 hover:text-grey-5 h-fit text-mobile-14 text-right hover:cursor-pointer hover:underline mt-2 lg:hidden"
              onClick={handleForgotPassword}
            >
              Forgot Password?
            </p>
          </div>

          {/* recaptcha */}
          <div className="flex flex-row justify-between gap-4 mt-8">
            <div className="sm:flex self-center h-[74px]">
              <ReCAPTCHA
                data-size="compact"
                ref={recaptchaRef}
                sitekey={import.meta.env.PUBLIC_RECAPTCHA_SITEKEY}
                onChange={onRecaptchaChange}
                data-action="LOGIN"
              />
            </div>
            <p
              id="forgot-password"
              className="text-grey-4 hover:text-black text-mobile-14 text-right sm:text-center hover:cursor-pointer hover:underline max-lg:hidden h-fit"
              onClick={handleForgotPassword}
            >
              Forgot Password?
            </p>
          </div>

          {/* login button */}
          <button
            id="loginBtn"
            type="submit"
            className="bg-primary-6 font-semibold px-5 py-3 rounded text-white hover:bg-primary-7 transition-all w-full mt-6"
          >
            LOG IN
          </button>
        </form>

        {/* navigate to sign up */}
        {isRegistration && (
          <p className="text-center sm:text-left *:text-mobile-14">
            <span>Haven't registered? </span>
            <a href="/sign-up" className="text-primary-6 hover:underline">
              Sign up here
            </a>
          </p>
        )}
      </div>
    </div>
  );
};

export default SignIn;
