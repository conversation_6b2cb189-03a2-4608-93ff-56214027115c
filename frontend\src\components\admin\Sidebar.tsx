import { useContext } from "react";
import { widget, actionSessionStorage, auth } from "@/utils";
import { AdminContext } from "./Context";

const PAGE_LIST = [
  {
    id: "status",
    title: "Evaluation Status",
    icon: "fa-regular fa-chart-bar",
  },
  {
    id: "assign",
    title: "Project Distribution",
    icon: "fa-solid fa-gears",
  },
  {
    id: "video-qc",
    title: "Video QC Dashboard",
    icon: "fa-solid fa-video",
  },
  {
    id: "airtable-project",
    title: "Airtable Project View",
    icon: "fa-solid fa-file",
  },
  {
    id: "airtable-evaluation",
    title: "Airtable Evaluation View",
    icon: "fa-solid fa-gavel",
  },
  {
    id: "special-access",
    title: "Special Access",
    icon: "fa-regular fa-address-card",
  },
];

const Sidebar = () => {
  const { page, setPage } = useContext(AdminContext);

  async function logOut() {
    ``;
    const confirm = await widget.confirm(
      "Are you sure?",
      "Log out from current session?",
      "Yes, log out"
    );
    if (!confirm.isConfirmed) return;

    actionSessionStorage().clear();
    auth.signOut();
    window.location.href = "/sign-in";
  }

  return (
    <div
      id="sidebar"
      className="bg-white lg:h-full w-full lg:w-[88px] transition-all duration-300 relative  lg:border-r-2"
    >
      <div className=" flex flex-row lg:flex-col w-full justify-between lg:h-full bg-white p-5">
        {/* Menu buttons */}
        <div className="flex flex-row lg:flex-col gap-3 ">
          {/* Profile Picture */}
          <div
            className="rounded-full size-12 ring ring-primary-3 shadow-[5px_5px_9px_#cfcfcf_,_-5px_-5px_9px_#ffffff]"
            title="pixie"
          >
            <div className="size-12 rounded-full max-w-none overflow-hidden bg-white">
              <img
                src="/assets/images/logo/pixie-half.webp"
                alt="Avatar"
                className="h-full w-full object-contain"
              />
            </div>
          </div>

          {PAGE_LIST.map((item, index) => (
            <button
              key={index}
              className={`flex flex-row items-center justify-center h-12 w-12 rounded-full transition hover:bg-primary-1 hover:text-primary-6 shadow-[5px_5px_9px_#cfcfcf_,_-5px_-5px_9px_#ffffff] border border-grey-1 hover:border-none ${
                page == item.id ? "bg-primary-1 text-primary-6" : ""
              }`}
              title={item.title}
              onClick={() => setPage(item.id)}
            >
              <i className={item.icon}></i>{" "}
            </button>
          ))}
        </div>
        {/* Logout */}
        <div
          className="flex flex-row justify-center items-center rounded-full size-12 bg-red-500 hover:bg-red-600 transition duration-200 ease-in-out hover:cursor-pointer shadow-lg"
          title="Sign Out"
          onClick={logOut}
          id="logoutbtn"
        >
          <i className="fa-solid fa-arrow-right-from-bracket text-white"></i>
        </div>
      </div>
    </div>
  );
};
export default Sidebar;
