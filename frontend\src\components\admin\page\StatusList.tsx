import { useState, useContext } from "react";
import Loading from "@components/general/Loading";
import { AdminContext } from "../Context";

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;

const EvaluationStatus = () => {
  const { judges, projects } = useContext(AdminContext);

  const [currentList, setCurrentList] = useState<String>("judges");

  const judgesForCurrentYear =
    judges?.filter((judge) => judge.year.includes(currentYear)) || [];

  return (
    <>
      <div className="flex gap-3 ">
        <div className="bg-blue-300 w-4 rounded-md" />
        <p className="font-semibold">Evaluation Status</p>
      </div>

      {/* button to set either view evaluation status by judges or projects */}
      <div className="flex justify-between mt-5">
        <div className="flex gap-2">
          <button
            className={`daisy-btn text-primary-6 rounded transition hover:shadow border-primary-6 ${
              currentList === "judges"
                ? "bg-primary-6 text-white hover:bg-primary-6 border-primary-6  "
                : "hover:-translate-y-0.5 hover:shadow-sm transition bg-white hover:bg-white hover:border-primary-7"
            }`}
            onClick={() => setCurrentList("judges")}
          >
            By Judges
          </button>
          <button
            className={`daisy-btn text-primary-6 rounded transition hover:shadow  border-primary-6 ${
              currentList === "projects"
                ? "bg-primary-6 text-white hover:bg-primary-6 border-primary-6  "
                : "hover:-translate-y-0.5 hover:shadow-sm transition bg-white hover:bg-white hover:border-primary-7"
            }`}
            onClick={() => setCurrentList("projects")}
          >
            By Projects
          </button>
        </div>
      </div>

      {/* judges or projects evaluation status list */}
      {currentList == "judges" ? (
        // evaluation status by judge
        <>
          <div className="mt-5 relative border border-solid rounded-md">
            <table className="w-full text-sm text-left " id="judgesTable">
              <thead className="text-xs uppercase border-b">
                <tr>
                  <th scope="col" className="px-6 py-4">
                    Judge Name
                  </th>
                  <th scope="col" className="px-6 py-3">
                    Projects Assigned
                  </th>
                  <th scope="col" className="px-6 py-3">
                    Status
                  </th>
                </tr>
              </thead>

              {/* Show loading state when the judges is blank in the initial state */}
              {judges && judges.length === 0 && (
                <tbody>
                  <tr>
                    <td colSpan={3}>
                      <div
                        role="status"
                        className="w-full text-center mx-auto flex justify-center col-span-3 my-5"
                      >
                        <Loading />
                        <span className="sr-only">Fetching data...</span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              )}

              <tbody>
                {judgesForCurrentYear?.map((judge, i) => (
                  <tr key={i} className="bg-white border-b">
                    {/* Column 1: Judge Name */}
                    <td
                      scope="row"
                      className="px-4 py-4 font-medium whitespace-nowrap "
                    >
                      <p
                        onClick={() => alert(judge.id)}
                        className="hover:underline transition hover:text-primary-6 cursor-pointer flex"
                      >
                        {/* red ping - yet to complete all evaluation */}
                        {/* green ping - completed all evaluation */}
                        <span className="relative flex h-3 items-center w-3">
                          {judge.evaluated_projects_name &&
                          judge.evaluated_projects_name?.length ===
                            judge.assigned_projects_id?.length ? (
                            <>
                              <span className="animate-ping absolute inline-flex -left-0.5 h-full w-full rounded-full bg-green-400 opacity-75"></span>
                              <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
                            </>
                          ) : (
                            <>
                              <span className="animate-ping absolute inline-flex -left-0.5 h-full w-full rounded-full bg-red-400 opacity-75"></span>
                              <span className="relative inline-flex rounded-full h-2 w-2 bg-red-500"></span>
                            </>
                          )}
                        </span>

                        {judge.name}
                      </p>
                    </td>

                    {/* Column 2: Assigned projects */}
                    <td className="px-6 py-4">
                      {judge.assigned_projects_id &&
                        judge.assigned_projects_id.map(
                          (project_id, i) =>
                            judge.assigned_projects_year[i] == currentYear && (
                              <p
                                key={i}
                                onClick={() => alert(project_id)}
                                className={`hover:underline transition hover:text-primary-6 cursor-pointer ${judge.evaluated_projects_id?.includes(project_id) ? "text-green-500" : "text-red-500"}`}
                              >
                                {judge.project_name[i]}
                              </p>
                            )
                        )}
                    </td>

                    {/* Column 3: Evaluation Status */}
                    <td className="px-6 py-4 flex ">
                      {judge.evaluated_projects_year &&
                      Array.isArray(judge.evaluated_projects_year)
                        ? judge.evaluated_projects_year.filter(
                            (year: string) => year === currentYear.toString()
                          ).length
                        : 0}
                      /
                      {judge.assigned_projects_id
                        ? judge.assigned_projects_year.filter(
                            (year: string) => year === currentYear.toString()
                          ).length
                        : 0}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </>
      ) : (
        // evaluation status by project
        <>
          <div className="mt-5 relative border border-solid rounded-md">
            <table className="w-full text-sm text-left " id="projectsTable">
              <thead className="text-xs text-cs-white uppercase border-b">
                <tr>
                  <th scope="col" className="px-6 py-4">
                    Project name
                  </th>
                  <th scope="col" className="px-6 py-3">
                    Judges Assigned
                  </th>
                  <th scope="col" className="px-6 py-3">
                    Status
                  </th>
                </tr>
              </thead>

              {/* No project available */}
              {projects && !projects[0] && (
                <tbody>
                  <tr>
                    <td colSpan={3}>
                      <div className="w-full text-center mx-auto flex justify-center col-span-3 my-5">
                        <p className="text-gray-500 text-sm">
                          No projects being assigned
                        </p>
                      </div>
                    </td>
                  </tr>
                </tbody>
              )}

              <tbody className="bg-white divide-y dark:divide-gray-700 dark:bg-gray-800">
                {projects?.map(
                  (project, i) =>
                    // only show projects that has assigned judges
                    judgesForCurrentYear.filter((judge) =>
                      judge.assigned_projects_id?.includes(project?.id)
                    ).length > 0 && (
                      <tr key={i} className="bg-white border-b">
                        {/* Column 1: Project Name */}
                        <td
                          scope="row"
                          className="px-4 py-4 font-medium break-words  w-[40%]"
                        >
                          <p
                            onClick={() => alert(project.id)}
                            className="hover:underline transition hover:text-primary-6 cursor-pointer"
                          >
                            {project.project_name}
                          </p>
                        </td>

                        {/* Column 2: Judge Assigned Name */}
                        {/* name in green - evaluated */}
                        {/* name in red - not yet evaluated */}
                        <td className="px-6 py-4">
                          {/* find all judges that is assigned to this project */}
                          {judgesForCurrentYear
                            .filter((judge) =>
                              judge.assigned_projects_id?.includes(project.id)
                            )
                            .map((judge, i) => (
                              <p
                                key={i}
                                onClick={() => alert(judge.id)}
                                className={`hover:underline transition hover:text-primary-6 cursor-pointer ${
                                  judge.evaluated_projects_id?.includes(
                                    project.id
                                  )
                                    ? "text-green-500"
                                    : "text-red-500"
                                }`}
                              >
                                {judge.name}
                              </p>
                            ))}
                        </td>

                        {/* Column 3: Project Evaluation Status */}
                        <td className="px-6 py-4 flex ">
                          <p className="">
                            {/* show the number assigned and evaluated */}
                            {/* count how many judges has evaluated the projects */}
                            {judgesForCurrentYear.reduce(
                              (acc, curr) =>
                                curr.evaluated_projects_id?.includes(project.id)
                                  ? acc + 1
                                  : acc,
                              0
                            )}
                            /
                            {/* count how many judges has been assigned to the project */}
                            {
                              judgesForCurrentYear.filter((judge) =>
                                judge.assigned_projects_id?.includes(project.id)
                              ).length
                            }
                          </p>
                        </td>
                      </tr>
                    )
                )}
              </tbody>
            </table>
          </div>
        </>
      )}
    </>
  );
};

export default EvaluationStatus;
