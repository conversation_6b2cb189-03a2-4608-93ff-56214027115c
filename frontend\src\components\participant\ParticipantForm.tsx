import type { ParticipantInfo } from "@/modules";
import { useEffect, useState, useId, useRef, ChangeEvent } from "react";
//LMNTOFIX
// import { authFactory, Participant } from "@/modules";
import { authFactory, participantFactory, Participant } from "@/modules";
import {
  widget,
  firebaseStorage,
  auth,
  compressedImage,
  actionSessionStorage,
  googleDriveUpload,
} from "@/utils";
import {
  deleteObject,
  getDownloadURL,
  getStorage,
  ref,
  uploadBytes,
} from "firebase/storage";
import * as Yup from "yup";
import { onAuthStateChanged } from "firebase/auth";
import BaseModal from "@components/general/BaseModal";
import ProfileModal from "@components/general/ProfileModal";
import UpdatePhotoModal from "./UpdatePhotoModal";

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
const resumeFolderId = import.meta.env.PUBLIC_GDRIVE_RESUME_FOLDER;

const ParticipantForm: React.FC = () => {
  const user: Partial<Participant> = actionSessionStorage().get("userData");
  const team = actionSessionStorage().get("projectData");

  const imageId = useId();
  const imageRef = useRef<any>();
  const modalRef = useRef<any>();
  const profileModalRef = useRef<any>();
  const updatePhotoModalRef = useRef<any>();

  // To solve aria-hidden warning
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const [cropper, setCropper] = useState<any>();
  const [userUid, setUserUid] = useState<string | undefined>(user.firebase_uid);
  // const [userIndex, setUserIndex] = useState<any>(); // user index in team

  const [student_name, setstudent_name] = useState<string | undefined>(
    user.student_name
  );
  const [display_name, setdisplay_name] = useState<string | undefined>(
    user.display_name
  );
  const [matric_no, setmatric_no] = useState<string | undefined>(
    user.matric_no
  );
  // not showing ic
  const [student_email, setstudent_email] = useState<string | undefined>(
    user.student_email
  );
  const [student_major, setstudent_major] = useState<string | undefined>(
    user.student_major
  );
  const [desc, setDesc] = useState<string | undefined>(user.desc);

  const [personal_email, setpersonal_email] = useState<string | undefined>(
    user.personal_email
  );
  const [tel, setTel] = useState<string | undefined>(user.tel);
  const [discord_id, setDiscord] = useState<string | undefined>(
    user.discord_id
  );
  const [github, setGithub] = useState<string | undefined>(user.github);
  const [linkedin, setLinkedin] = useState<string | undefined>(user.linkedin);
  const [portfolio, setPortfolio] = useState<string | undefined>(
    user.portfolio
  );

  const [profileImage, setProfileImage] = useState<File | null>(null);
  const [publicUrl, setPublicUrl] = useState<string | undefined>(user.photo);
  const [textCount, setTextCount] = useState(0);
  const [resume, setResume] = useState<string | undefined>(user.resume);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  // TODO: user email did not get
  // TODO: they can edit through html the email as we have already put email value in the form
  const [imageDetail, setImageDetail] = useState<any>({
    src: user.photo || "https://www.w3schools.com/howto/img_avatar.png",
    alt: "Avatar",
  });

  const handlePhotoUpdate = (photo: File) => {
    setProfileImage(photo);
    // Preview the image immediately
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        setImageDetail({
          ...imageDetail,
          src: e.target.result as string,
        });
      }
    };
    reader.readAsDataURL(photo);
  };

  useEffect(() => {
    const wordCount = user.desc?.trim().split(/\s+/).length!;

    setTextCount(wordCount);

    // setUserIndex(
    //   team?.mpembers_name?.findIndex(
    //     (name: string) => name === user?.student_name
    //   )
    // );
  }, []);

  const handleSubmit = async (e: any) => {
    e.preventDefault();

    if (!resume) {
      widget.alertError("Error", "Please upload your resume (PDF, max 5MB).");
      return;
    }

    let registerDetails: Partial<ParticipantInfo> = {
      student_name: e.target["student_name"].value,
      display_name: e.target["display_name"].value,
      matric_no: e.target["matric_no"].value,
      ic: user.ic,
      student_email: e.target["student_email"].value,
      student_major: user.student_major,
      desc: e.target["desc"].value,

      personal_email: e.target["personal_email"].value,
      tel: e.target["tel"].value,
      discord_id: e.target["discord_id"].value,
      github: e.target["github"].value,
      linkedin: e.target["linkedin"].value,
      portfolio: e.target["portfolio"].value,
      resume: resume,
    };

    if (profileImage) {
      registerDetails.photo = "pending_upload"; // Placeholder until the upload is complete
    }

    // agreement: Yup.mixed().notRequired(),
    // photo_agreement: Yup.mixed().notRequired(),

    // firebase_uid: Yup.mixed().notRequired(),
    // created_at: Yup.mixed().notRequired(),
    // updated_at: Yup.mixed().notRequired(),

    let registerSchema = Yup.object().shape<
      Record<
        keyof Omit<
          ParticipantInfo,
          | "id"
          | "project_id"
          | "agreement"
          | "photo_agreement"
          | "resume_agreement"
          | "firebase_uid"
          | "created_at"
          | "updated_at"
          | "photo"
          | "student_email"
          | "student_major"
          | "ic"
          | "photo"
          | "year"
        >,
        Yup.AnySchema
      >
    >({
      student_name: Yup.string().trim().required("Please enter your name"),
      display_name: Yup.string()
        .trim()
        .required("Please enter your display name"),
      matric_no: Yup.number()
        .required()
        .typeError("Please enter a valid matric number"),
      tel: Yup.string()
        .matches(
          /^[0-9]{3,15}$/g,
          "Please enter a valid phone number. Example: 0123456789"
        )
        .trim()
        .required(),
      personal_email: Yup.string()
        .email("Please enter a valid email")
        .trim()
        .required(),
      github: Yup.string()
        .matches(
          /^https:\/\/github\.com\/[a-zA-Z0-9-]+\/?$/,
          "Invalid URL, please use a proper Github Profile link. Example: https://github.com/Arithz"
        )
        .trim()
        .required(),
      linkedin: Yup.string()
        .matches(
          /^https:\/\/www\.linkedin\.com\/in\/[a-zA-Z0-9-]+\/?$/,
          "Invalid URL, please use a proper LinkedIn Profile link. Example: https://www.linkedin.com/in/muhammad-ammar-adnan/"
        )
        .trim()
        .required(),
      discord_id: Yup.string()
        .nullable()
        .notRequired()
        .trim()
        .when({
          is: (val: string) => !!val, // only validate if not empty
          then: (schema) =>
            schema.matches(
              /^[^#]{2,32}#\d{4}$|^(?!.*\.\.|[A-Z])[a-z\d_.]{2,32}$/,
              "Invalid Discord ID, please enter a valid Discord ID. Example: username1234"
            ),
        }),
      portfolio: Yup.string()
        .url("Please enter a valid portfolio url")
        .trim()
        .optional(),
      resume: Yup.mixed(),
      desc: Yup.string().trim().required("Please tell us about yourself"),
    });
    try {
      // validate form data

      console.log(registerDetails.discord_id);
      console.log(registerDetails.portfolio);

      await registerSchema.validate(registerDetails);

      // check if user want to proceed with the update
      const confirm = await widget.confirm(
        "Are you sure?",
        "Are you certain that the information you have provided is accurate and complete? Would you like to proceed with this submission?"
      );
      if (!confirm.isConfirmed) return;

      widget.loading();
      // update user information
      return await updateUserAccount(registerDetails);
    } catch (error: any) {
      return await widget.alertError("Sorry", error.message);
    }
  };

  async function updateUserAccount(registerDetails: Partial<ParticipantInfo>) {
    try {
      widget.loading();
      // let response: any;

      // Handle profile image upload if there's a new one
      if (profileImage) {
        // Compress image
        const webpFile: any = await compressedImage(profileImage);

        const fileName = registerDetails.ic + "_PHOTO";
        const url = await firebaseStorage.uploadImageFirebase(
          webpFile,
          fileName,
          "participant/" + currentYear
        );
        setPublicUrl(url);
        registerDetails.photo = url;
      }

      // ensure only field with same name but different values are in registerDetails for update
      const actionSessionStorageData = actionSessionStorage().get("userData");
      for (const key in registerDetails) {
        delete registerDetails["project_id"];
        if (
          registerDetails[key] === actionSessionStorageData[key] &&
          key !== "project_id"
        ) {
          delete registerDetails[key];
        }
      }

      // no data changed
      if (Object.values(registerDetails).length === 0) {
        window.location.href = "/participant";
        widget.close();
        return;
      }

      // if (user?.project_id) registerDetails.project_id = user?.project_id;
      // }

      await authFactory().updateAccount(registerDetails, userUid);
      // if (!response)
      //   throw new Error("Something went wrong. Please try again later.");

      // update session storage
      // team.members_name[userIndex] = registerDetails.name!;
      // if (response?.profile_pic) team.members_urls[userIndex] = response.profile_pic;
      // actionSessionStorage().update("projectData", team);

      await widget.alertSuccess(
        "Success",
        "Your profile has been updated successfully."
      );

      window.location.href = "/participant";
    } catch (error: any) {
      await widget.alertError("Error in updateUserAccount", error);
    }
  }

  function handleAboutMeChange(e: any) {
    const wordCount = e.target.value.trim().split(/\s+/).length;

    if (wordCount > 100) return;

    setTextCount(wordCount);
    setDesc(e.target.value);
  }

  async function handleResumeUpload(e: any) {
    try {
      const file = e.files[0];
      // Validate file size
      if (file.size > 5000000) {
        return await widget.alertError("Error", "File size is too large!");
      }

      // Validate file type
      if (file.type !== "application/pdf") {
        return await widget.alertError("Sorry", "Please upload a PDF file!");
      }
      setIsUploading(true);
      setUploadProgress(10);

      const reader = new FileReader();

      // Read file as data URL
      const dataUrl = await new Promise<string>((resolve, reject) => {
        reader.onload = () => {
          setUploadProgress(30);
          resolve(reader.result as string);
        };
        reader.onerror = (error) => reject(error);
        reader.readAsDataURL(file);
      });
      setUploadProgress(50);

      // Upload to Google Drive
      const fileUrl = await googleDriveUpload(
        file,
        dataUrl,
        resumeFolderId
      );
      setUploadProgress(100);
      setResume(fileUrl);

      // Reset progress after a short delay
      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);
      }, 1000);
    } catch (error) {
      setIsUploading(false);
      setUploadProgress(0);
      return await widget.alertError(
        "Error",
        "Failed to upload file. Please try again."
      );
    }
  }

  async function handleCancel() {
    const confirm = await widget.confirm(
      "Are you sure?",
      "Are you certain to cancel the edit?"
    );
    if (!confirm.isConfirmed) return;
    window.location.href = "/participant";
  }

  return (
    <div className="h-full flex flex-col md:justify-center px-4 md:px-16 lg:px-20 py-8 w-full">
      <div className="flex flex-col md:flex-row  md:items-center">
        <a href="/participant">
          <i
            title="Back to Dashboard"
            className="relative fa-solid fa-arrow-left flex justify-center items-center max-md:size-8 size-6 md:-ml-8  text-xs rounded-full border border-grey-4 transition duration-100 cursor-pointer hover:bg-primary-2 hover:border-primary-6 hover:text-primary-6"
          ></i>
        </a>

        <div className="text-mobile-14 max-md:mt-2">
          <h1 className="font-bold text-mobile-20 lg:text-tablet-24">
            Edit Profile
          </h1>
        </div>
      </div>
      <form onSubmit={handleSubmit}>
        <div className="grid md:grid-cols-2 md:gap-12 mt-5">
          {/* Left side */}
          <div className="flex flex-col w-full space-y-3">
            {/* General */}
            <input
              type="text"
              id="emailaddress"
              name="student_email"
              defaultValue={student_email}
              required
              readOnly
              disabled
              className="daisy-input cursor-not-allowed disabled:text-grey-3"
            />
            <input
              type="text"
              placeholder="Full Name"
              className="daisy-input capitalize"
              defaultValue={user?.student_name}
              name="student_name"
              required
              onChange={(e: ChangeEvent<HTMLInputElement>) => {
                e.target.value = e.target.value.replace(/\b\w/g, (l) =>
                  l.toUpperCase()
                );
                setstudent_name(e.target.value);
              }}
            />
            <input
              type="text"
              placeholder="Display Name"
              className="daisy-input "
              name="display_name"
              defaultValue={user?.display_name}
              onChange={(e: ChangeEvent<HTMLInputElement>) =>
                setdisplay_name(e.target.value)
              }
            />

            <div className="w-full flex flex-col lg :flex-row gap-4 md:gap-2">
              <input
                type="text"
                placeholder="Matric Number"
                name="matric_no"
                defaultValue={user?.matric_no}
                className="daisy-input"
                onChange={(e: ChangeEvent<HTMLInputElement>) =>
                  setmatric_no(e.target.value)
                }
              />

              <input
                type="text"
                id="emailaddress"
                name="email"
                defaultValue={user.student_major}
                required
                readOnly
                disabled
                className="daisy-input cursor-not-allowed disabled:text-grey-3"
              />
            </div>

            <input
              type="text"
              placeholder="Personal Email"
              name="personal_email"
              defaultValue={user?.personal_email}
              className="daisy-input"
              onChange={(e: ChangeEvent<HTMLInputElement>) =>
                setpersonal_email(e.target.value)
              }
            />

            <input
              type="text"
              placeholder="Phone No. (without '-')"
              name="tel"
              defaultValue={user?.tel}
              className="daisy-input"
              onChange={(e: ChangeEvent<HTMLInputElement>) =>
                setTel(e.target.value)
              }
            />

            <h2 className="underline underline-offset-8 font-semibold !mt-5">
              Social Network
            </h2>

            {/* Social Network */}
            <div className="flex flex-col gap-3 justify-center !mt-5">
              <div className="flex gap-3 items-center">
                <i className="hidden sm:flex fa-brands fa-github fa-xl w-[32px] justify-center"></i>
                <input
                  type="text"
                  placeholder="https://github.com/johndoe"
                  name="github"
                  className="daisy-input w-full"
                  defaultValue={user?.github}
                  onChange={(e: ChangeEvent<HTMLInputElement>) =>
                    setGithub(e.target.value)
                  }
                />
              </div>
              <div className="flex gap-3 items-center">
                <i className="hidden sm:flex fa-brands fa-linkedin fa-xl w-[32px] justify-center"></i>
                <input
                  type="text"
                  placeholder="https://www.linkedin.com/in/johndoe/"
                  name="linkedin"
                  className="daisy-input w-full"
                  defaultValue={user?.linkedin}
                  onChange={(e: ChangeEvent<HTMLInputElement>) =>
                    setLinkedin(e.target.value)
                  }
                />
              </div>
              <div className="flex gap-3 items-center">
                <i className="hidden sm:flex fa-brands fa-discord fa-xl w-[32px] justify-center"></i>
                <input
                  type="text"
                  placeholder="Discord ID (optional)"
                  name="discord_id"
                  className="daisy-input w-full"
                  defaultValue={user?.discord_id}
                  onChange={(e: ChangeEvent<HTMLInputElement>) =>
                    setDiscord(e.target.value)
                  }
                />
              </div>

              <div className="flex gap-3 items-center">
                <i className="hidden sm:flex fa-solid fa-globe fa-xl w-[32px] justify-center"></i>
                <input
                  type="text"
                  placeholder="Portfolio website url (optional)"
                  name="portfolio"
                  className="daisy-input w-full"
                  defaultValue={user?.portfolio}
                  onChange={(e: ChangeEvent<HTMLInputElement>) =>
                    setPortfolio(e.target.value)
                  }
                />
              </div>
            </div>
          </div>

          {/* Right side */}
          <div className="flex flex-col w-full h-full justify-between">
            <div>
              {/* Profile Image */}
              <div>
                <h2 className="underline underline-offset-8 font-semibold max-md:mt-5">
                  Profile Image
                </h2>
                <div className="relative flex justify-center mt-5">
                  <div className="rounded-full overflow-hidden relative bg-white group">
                    <img
                      src={imageDetail.src}
                      alt={imageDetail.alt}
                      className="mb-4 rounded-full object-contain size-32 xl:size-40 border-2 border-grey-2"
                    />
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div
                        className="bg-grey-4 bg-opacity-60 text-white px-2 py-1 rounded text-sm cursor-pointer"
                        onClick={() => {
                          const modal = document.getElementById(
                            "update_photo_modal"
                          ) as HTMLDialogElement;
                          modal?.showModal();
                        }}
                      >
                        Update Photo
                      </div>
                    </div>
                  </div>
                </div>
                <UpdatePhotoModal
                  id="update_photo_modal"
                  currentPhoto={imageDetail.src}
                  onPhotoUpdate={handlePhotoUpdate}
                />
              </div>
              {/* About Me */}
              <h2 className="underline underline-offset-8 font-semibold !mt-5">
                About Me
              </h2>
              <textarea
                placeholder="Tell us about yourself..."
                name="desc"
                className="w-full daisy-textarea  mt-5 text-base"
                required
                rows={10}
                defaultValue={desc}
                onChange={(e: ChangeEvent<HTMLTextAreaElement>) =>
                  handleAboutMeChange(e)
                }
              />
              <p className="text-right text-grey-4 mt-1">
                {desc ? textCount : 0} / 100
              </p>

              {/* Resume */}
              <div>
                <h2 className="underline underline-offset-8 font-semibold !mt-5">
                  Resume
                </h2>

                <div className="label flex flex-col items-center justify-center transition-all duration-500 ease-in-out">
                  <span className="text-black/70 w-full h-20 rounded-lg bg-primary-1 flex items-center justify-center transition-all duration-500 ease-in-out px-2 mt-3 md:mt-4 lg:mt-5">
                    <div className="flex flex-row items-center justify-center gap-2">
                      {resume && (
                        <a
                          href={
                            typeof resume === "string"
                              ? resume
                              : URL.createObjectURL(resume)
                          }
                          target="_blank"
                          rel="noopener noreferrer"
                          className="pl-4 gap-2 flex flex-row items-center rounded-lg w-[304px] h-[45px] bg-primary-2 text-white transition-opacity duration-500 ease-in-out cursor-pointer"
                        >
                          <span className="pl-2 text-base text-black transition-all duration-500 ease-in-out">
                            View Uploaded Resume
                          </span>
                        </a>
                      )}

                      {/* Only this label triggers file input */}
                      <label
                        htmlFor="resume"
                        className="underline cursor-pointer transition-all duration-500 ease-in-out text-base"
                      >
                        Choose File
                      </label>

                      <input
                        accept="application/pdf"
                        type="file"
                        id="resume"
                        name="resume"
                        className="hidden"
                        onChange={(e: any) => {
                          handleResumeUpload(e.target);
                        }}
                      />
                    </div>
                  </span>
                </div>

                <div className="flex justify-between">
                  <p className="mt-1 text-sm text-grey-3" id="file_input_help">
                    PDF only (Max size 5MB).
                  </p>
                </div>
                {/* Progress Bar */}
                {isUploading && (
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <div
                        className="bg-black h-2.5 rounded-full transition-all duration-300 ease-in-out"
                        style={{ width: `${uploadProgress}%` }}
                      ></div>
                    </div>
                    <p className="text-sm mt-1">
                      {uploadProgress < 100
                        ? "Uploading..."
                        : "Upload complete!"}
                    </p>
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end mt-3 md:mx-0 space-x-3">
              <button
                type="button"
                className="bg-dangerColor px-5 py-2 rounded text-white transition duration-200 hover:-translate-y-0.5 hover:shadow-md"
                onClick={handleCancel}
              >
                Cancel
              </button>
              <button
                data-modal-target="profileModal"
                data-modal-toggle="profileModal"
                className="border border-primary-6  rounded text-primary-6 transition duration-200 hover:-translate-y-0.5 hover:shadow-md px-5 py-2"
                type="button"
                onClick={() => profileModalRef.current.showModal()}
              >
                Preview
              </button>
              <ProfileModal
                ref={profileModalRef}
                student_name={student_name}
                display_name={display_name}
                student_major={student_major}
                matric_no={matric_no}
                desc={desc}
                student_email={student_email}
                linkedin={linkedin}
                github={github}
                portfolio={portfolio}
                // imageURL={imageDetail.src}
                photo={imageDetail.src}
              />
              <button
                type="submit"
                id="submit"
                className="bg-primary-6 hover:bg-primary-7 transition-all  rounded text-white duration-200 hover:-translate-y-0.5 hover:shadow-md px-5 py-2"
              >
                Update
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default ParticipantForm;
