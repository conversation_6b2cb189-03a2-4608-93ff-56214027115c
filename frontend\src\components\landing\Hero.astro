---
const categories = [
  {
    name: "Computing Infrastructure",
    icon: "fa-solid fa-building-lock text-[64px] lg:text-[80px] xl:text-[96px]",
    gridPosition: "lg:row-start-2 lg:row-span-2",
    backgroundColor: "bg-[#70A1FC]",
  },
  {
    name: "Intelligent Computing",
    icon: "fa-solid fa-robot text-[64px] lg:text-[80px] xl:text-[96px]",
    gridPosition: "lg:row-start-1 lg:row-span-2",
    backgroundColor: "bg-[#FF695F]",
  },
  {
    name: "Software Engineering",
    icon: "fa-solid fa-circle-nodes text-[64px] lg:text-[80px] xl:text-[96px]",
    gridPosition: "lg:row-start-3 lg:row-span-2 lg:right-[20px]",
    backgroundColor: "bg-[#8CFF91]",
  },
];

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
const isRegistration = import.meta.env.PUBLIC_PIXEL_IS_REGISTRATION === "true";
const isJudging = import.meta.env.PUBLIC_PIXEL_IS_JUDGING === "true";
const isOfficialLaunch =
  import.meta.env.PUBLIC_PIXEL_IS_OFFICIAL_LAUNCH === "true";
const isPostSubmission =
  import.meta.env.PUBLIC_PIXEL_IS_POST_SUBMISSION === "true";
const isPostEvent = import.meta.env.PUBLIC_PIXEL_IS_POST_EVENT === "true";
---

<div
  class="pt-40 pb-20 md:pt-36 md:pb-16 lg:pt-36 lg:pb-24 px-4 md:px-5 min-h-[100svh] relative flex items-center"
>
  <div
    class="flex md:max-lg:flex-col justify-center lg:justify-between container-max-w items-center md:max-lg:gap-20 w-full"
  >
    <!-- landing text -->
    <div>
      <h1 class="max-md:text-center">PIXEL {currentYear}</h1>
      <p
        class="max-lg:text-center mt-6 max-w-[376px] lg:max-w-[440px] md:text-tablet-20 lg:text-desktop-24 !font-normal"
      >
        Project Innovation & eXploration in CS Education and Learning {currentYear}
      </p>
      <div class="flex max-md:flex-col max-lg:justify-center gap-3 mt-14">
        {
          isRegistration ? (
            <a
              href="/sign-up"
              class="w-full md:w-[156px] md:h-[60px] bg-primary-6 daisy-btn text-white md:text-mobile-18 !font-semibold hover:bg-primary-7 border-none"
            >
              Register
            </a>
          ) : isPostSubmission || isJudging ? (
            // after submission closed, show projects
            <a
              href="/projects"
              class="w-full md:w-[220px] md:h-[60px] bg-primary-6 daisy-btn text-white md:text-mobile-18 !font-semibold hover:bg-primary-7 border-none"
            >
              See {currentYear} Projects
            </a>
          ) : isPostEvent ? (
            <a
              href={`/awards/${currentYear}`}
              class="w-full md:w-[220px] md:h-[60px] bg-primary-6 daisy-btn text-white md:text-mobile-18 !font-semibold hover:bg-primary-7 border-none"
            >
              See {currentYear} Winners
            </a>
          ) : (
            (!isOfficialLaunch || !isRegistration) && (
              <a
                href={`/awards/${parseInt(currentYear) - 1}`}
                class="w-full md:w-[220px] md:h-[60px] bg-primary-6 daisy-btn text-white md:text-mobile-18 !font-semibold hover:bg-primary-7 border-none"
              >
                See {parseInt(currentYear) - 1} Winners
              </a>
            )
          )
        }
        <a
          href="#about"
          class="w-full md:w-[156px] md:h-[60px] bg-white daisy-btn text-black md:text-mobile-18 hover:text-primary-6 !font-medium hover:border-primary-7 hover:bg-white border"
          >Learn more</a
        >
      </div>
      {
        isRegistration && (
          <div class="flex max-md:flex-col gap-2 mt-8 items-center">
            <i class="fa-solid fa-circle-exclamation text-[#EF0000] text-2xl" />
            <p class="max-lg:text-center text-mobile-14 max-md:px-2 max-w-[40ch]">
              Open only to Class {currentYear} Computer Science Final Year Students
              studying in Universiti Sains Malaysia
            </p>
          </div>
        )
      }
      <a
        href="#about"
        class="h-8 flex items-center group absolute bottom-6 left-[50%] -translate-x-[50%]"
      >
        <hr
          class="w-12 my-auto border-2 border-grey-4 group-hover:border-primary-6 transition-all duration-300 mx-auto"
        />
      </a>
    </div>

    <!-- major logo blocks -->
    <div class="lg:w-[45vw] max-w-[600px] flex justify-center">
      <div
        class="max-md:hidden grid grid-rows-4 grid-cols-2 grid-flow-col gap-6 md:max-lg:grid-rows-1 md:max-lg:grid-cols-3 md:gap-10 lg:gap-12 *:size-[144px] lg:*:size-[200px] xl:*:size-[240px] *:flex *:justify-center *:items-center *:rounded-3xl h-fit"
      >
        {
          categories.map((category) => (
            <a
              href={
                !isPostEvent
                  ? `/awards/${parseInt(currentYear) - 1}/${category.name.split(" (")[0]}`
                  : `/projects/${category.name.split(" (")[0]}`
              }
              class={`relative ${category.backgroundColor} bg-opacity-25  flex flex-col ${category.gridPosition} overflow-visible rotate-[4deg] hover:rotate-[0deg] hover:bg-opacity-100 hover:shadow-2xl hover:shadow-${category.backgroundColor} hover:z-5 hover:scale-105 transition-transform duration-300 ease-in-out rounded-3xl group`}
            >
              <div class="flex flex-col gap-2 lg:gap-4 justify-center items-center p-4">
                <i class={`${category.icon}`} />
                <p class="mx-2 text-tablet-12 lg:text-desktop-16 !leading-tight !font-bold text-center uppercase">
                  {category.name}
                </p>
              </div>
              <div
                class={`absolute size-[144px] lg:size-[200px] xl:size-[240px] ${category.backgroundColor} bg-opacity-25 rounded-3xl absolute left-3 top-3 flex flex-col gap-2 lg:gap-4 justify-center items-center p-4 *:opacity-5 group-hover:*:opacity-0 *:left-[-4] *:top-[-4] *:relative`}
              >
                <i class={`${category.icon}`} />
                <p class="mx-2 text-tablet-12 lg:text-desktop-16 !leading-tight !font-bold text-center uppercase">
                  {category.name}
                </p>
              </div>
            </a>
          ))
        }
      </div>
    </div>
  </div>
</div>
