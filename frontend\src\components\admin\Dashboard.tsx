import { useState, useEffect } from "react";
import Sidebar from "./Sidebar";
import { MainSection } from "./MainSection";
import StatusList from "./page/StatusList";
import {
  Judge,
  Project,
  ParticipantSession,
  adminFactory,
  projectFactory,
} from "@modules/index";
import { sortArray } from "@utils/utils";
import { widget } from "@utils/widget";
import { AdminContext } from "./Context";
import AssignProject from "./page/AssignProject";
import TransferProject from "./page/TransferProject";
import { onAuthStateChanged } from "firebase/auth";
import { auth } from "@utils/firebase";
import admins from "@data/admins.json";
import Loading from "@components/general/Loading";
import VideoQCForm from "./page/VideoQCForm";
import AirtableProjectView from "./page/AirtableProjectView";
import AirtableEvaluationView from "./page/AirtableEvaluationView";
import SpecialAccess from "./page/SpecialAccess";

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;

const Dashboard = () => {
  const [page, setPage] = useState("status");

  const [projects, setProjects] = useState<Project[]>([]);
  const [judges, setJudges] = useState<Judge[]>([]);
  const [participants, setParticipants] = useState<
    Partial<ParticipantSession>[]
  >([]);

  const [rerender, setRerender] = useState(false);

  const [assignedJudges, setAssignedJudges] = useState<Judge[]>([]);
  const [evaluatedJudges, setEvaluatedJudges] = useState<Judge[]>([]);

  const [isAdmin, setIsAdmin] = useState(false);

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    let unsub;

    (function () {
      unsub = onAuthStateChanged(auth, (user) => {
        if (unsub) {
          unsub();
        }

        if (!user || !admins.includes(user.email)) {
          widget
            .alertError(
              "Unauthorized",
              "You are not authorized to access this page.",
              undefined,
              3000
            )
            .then(() => {
              auth.signOut();
              window.location.href = "/";
            });
        } else if (user && admins.includes(user.email)) {
          setIsAdmin(true);
        }
      });
    })();
  }, []);

  useEffect(() => {
    // fetch all required data
    if (isAdmin) {
      (async () => {
        try {
          const response = await Promise.all([
            adminFactory().getJudges(currentYear),
            // get only current year projects
            projectFactory().getProjects({
              year: currentYear,
              isVideoQC: true, // only fetch projects that have been through video QC
              isNotDraft: true, // only fetch projects that are not drafts
            }),
          ]);

          setAssignedJudges(
            response[0].filter(
              (judge) => judge.assigned_projects_id?.length > 0
            )
          );
          setEvaluatedJudges(
            response[0].filter(
              (judge) => judge.evaluated_projects_id?.length > 0
            )
          );

          await sortArray({ array: response[0], key: "name", skipError: true });
          setJudges(response[0]);

          await sortArray({
            array: response[1],
            key: "project_name",
            skipError: true,
          });
          setProjects(response[1]);

          setIsLoading(false);
        } catch (e) {
          await widget.alertError(
            "Sorry",
            "Something went wrong while fetching the data",
            e
          );
        }
      })();
    }
  }, [rerender, isAdmin]);

  useEffect(() => {
    // fetch participant data only once
    if (isAdmin) {
      (async () => {
        try {
          const response = await adminFactory().getParticipants();

          if (response.length != 0) {
            sortArray({
              array: response,
              key: "student_name",
              skipError: true,
            });
          }
          setParticipants(response);

          // setIsLoading(false);
        } catch (e) {
          await widget.alertError(
            "Sorry",
            "Something went wrong while fetching the data: ",
            e
          );
        }
      })();
    }
  }, [isAdmin]);

  return !isLoading ? (
    <div className="w-full h-full max-lg:min-h-screen flex flex-col bg-white md:rounded-md overflow-hidden">
      <div className="flex flex-col lg:flex-row w-full h-full lg:h-[720px]">
        <AdminContext.Provider
          value={{
            judges,
            projects,
            participants,
            setParticipants,
            page,
            setPage,
            rerender,
            setRerender,
            assignedJudges,
            setAssignedJudges,
            evaluatedJudges,
            setEvaluatedJudges,
          }}
        >
          <Sidebar />

          <MainSection>
            {(() => {
              switch (page) {
                case "status":
                  return <StatusList />;
                case "assign":
                  return (
                    <>
                      <AssignProject />
                      <TransferProject />
                    </>
                  );
                case "video-qc":
                  return <VideoQCForm />;
                case "airtable-project":
                  return <AirtableProjectView />;
                case "airtable-evaluation":
                  return <AirtableEvaluationView />;
                case "special-access":
                  return <SpecialAccess />;
                default:
                  return <StatusList />;
              }
            })()}
          </MainSection>
        </AdminContext.Provider>
      </div>
    </div>
  ) : (
    <div className="h-screen lg:h-[720px] flex justify-center items-center">
      <Loading />
    </div>
  );
};

export default Dashboard;
